-- 创建穿搭照片生成记录表
-- 遵循 Tauri 开发规范的数据库设计原则

CREATE TABLE IF NOT EXISTS outfit_photo_generations (
    id TEXT PRIMARY KEY NOT NULL,
    project_id TEXT NOT NULL,
    model_id TEXT NOT NULL,
    product_image_path TEXT NOT NULL,
    product_image_url TEXT,
    prompt TEXT NOT NULL,
    negative_prompt TEXT,
    status TEXT NOT NULL DEFAULT 'Pending',
    workflow_id TEXT,
    comfyui_prompt_id TEXT,
    result_image_paths TEXT NOT NULL DEFAULT '[]',
    result_image_urls TEXT NOT NULL DEFAULT '[]',
    error_message TEXT,
    started_at INTEGER NOT NULL,
    completed_at INTEGER,
    generation_time_ms INTEGER,
    created_at INTEGER NOT NULL,
    updated_at INTEGER NOT NULL,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_outfit_photo_generations_project_id ON outfit_photo_generations(project_id);
CREATE INDEX IF NOT EXISTS idx_outfit_photo_generations_model_id ON outfit_photo_generations(model_id);
CREATE INDEX IF NOT EXISTS idx_outfit_photo_generations_status ON outfit_photo_generations(status);
CREATE INDEX IF NOT EXISTS idx_outfit_photo_generations_created_at ON outfit_photo_generations(created_at);
