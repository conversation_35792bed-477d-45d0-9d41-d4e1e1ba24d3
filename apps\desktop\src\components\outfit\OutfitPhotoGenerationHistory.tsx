/**
 * 穿搭照片生成历史记录组件
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Eye,
  Download,
  RotateCcw,
  Trash2,
  Filter,
  Search,
  Image as ImageIcon
} from 'lucide-react';
import type {
  OutfitPhotoGeneration,
  GenerationHistoryQuery,
  GenerationHistoryResponse
} from '../../types/outfitPhotoGeneration';
import { GenerationStatus } from '../../types/outfitPhotoGeneration';
import { OutfitPhotoGenerationService } from '../../services/outfitPhotoGenerationService';
import { LoadingSpinner } from '../LoadingSpinner';
import { Modal } from '../Modal';
import { DeleteConfirmDialog } from '../DeleteConfirmDialog';

interface OutfitPhotoGenerationHistoryProps {
  projectId?: string;
  modelId?: string;
  onRetryGeneration?: (generationId: string) => void;
}

const STATUS_COLORS = {
  [GenerationStatus.Pending]: 'text-yellow-600 bg-yellow-100',
  [GenerationStatus.Processing]: 'text-blue-600 bg-blue-100',
  [GenerationStatus.Completed]: 'text-green-600 bg-green-100',
  [GenerationStatus.Failed]: 'text-red-600 bg-red-100'
};

const STATUS_ICONS = {
  [GenerationStatus.Pending]: Clock,
  [GenerationStatus.Processing]: AlertCircle,
  [GenerationStatus.Completed]: CheckCircle,
  [GenerationStatus.Failed]: XCircle
};

const STATUS_LABELS = {
  [GenerationStatus.Pending]: '等待中',
  [GenerationStatus.Processing]: '生成中',
  [GenerationStatus.Completed]: '已完成',
  [GenerationStatus.Failed]: '失败'
};

export const OutfitPhotoGenerationHistory: React.FC<OutfitPhotoGenerationHistoryProps> = ({
  projectId,
  modelId,
  onRetryGeneration
}) => {
  const [records, setRecords] = useState<OutfitPhotoGeneration[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedRecord, setSelectedRecord] = useState<OutfitPhotoGeneration | null>(null);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [deleteConfirm, setDeleteConfirm] = useState<string | null>(null);

  // 查询参数
  const [query, setQuery] = useState<GenerationHistoryQuery>({
    project_id: projectId,
    model_id: modelId,
    page: 1,
    page_size: 20
  });

  // 分页信息
  const [totalCount, setTotalCount] = useState(0);
  const [hasMore, setHasMore] = useState(false);

  // 过滤器状态
  const [showFilters, setShowFilters] = useState(false);
  const [statusFilter, setStatusFilter] = useState<GenerationStatus | ''>('');
  const [searchText, setSearchText] = useState('');

  // 加载历史记录
  const loadHistory = useCallback(async (resetPage = false) => {
    try {
      setLoading(true);
      setError(null);

      const searchQuery: GenerationHistoryQuery = {
        ...query,
        page: resetPage ? 1 : query.page,
        status: statusFilter || undefined
      };

      const response: GenerationHistoryResponse = await OutfitPhotoGenerationService.getGenerationHistory(searchQuery);
      
      if (resetPage) {
        setRecords(response.records);
      } else {
        setRecords(prev => [...prev, ...response.records]);
      }
      
      setTotalCount(response.total_count);
      setHasMore(response.has_more);
      
      if (resetPage) {
        setQuery(prev => ({ ...prev, page: 1 }));
      }
    } catch (err) {
      console.error('加载生成历史失败:', err);
      setError(err instanceof Error ? err.message : '加载失败');
    } finally {
      setLoading(false);
    }
  }, [query, statusFilter]);

  // 初始加载
  useEffect(() => {
    loadHistory(true);
  }, [projectId, modelId, statusFilter]);

  // 加载更多
  const loadMore = useCallback(() => {
    if (!loading && hasMore) {
      setQuery(prev => ({ ...prev, page: prev.page! + 1 }));
      loadHistory(false);
    }
  }, [loading, hasMore, loadHistory]);

  // 重试生成
  const handleRetry = useCallback(async (record: OutfitPhotoGeneration) => {
    try {
      await OutfitPhotoGenerationService.retryGeneration(record.id);
      onRetryGeneration?.(record.id);
      // 重新加载历史记录
      loadHistory(true);
    } catch (err) {
      console.error('重试生成失败:', err);
      setError(err instanceof Error ? err.message : '重试失败');
    }
  }, [onRetryGeneration, loadHistory]);

  // 删除记录
  const handleDelete = useCallback(async (recordId: string) => {
    try {
      await OutfitPhotoGenerationService.deleteGeneration(recordId);
      setRecords(prev => prev.filter(r => r.id !== recordId));
      setDeleteConfirm(null);
    } catch (err) {
      console.error('删除记录失败:', err);
      setError(err instanceof Error ? err.message : '删除失败');
    }
  }, []);

  // 查看详情
  const handleViewDetail = useCallback((record: OutfitPhotoGeneration) => {
    setSelectedRecord(record);
    setShowDetailModal(true);
  }, []);

  // 格式化时间
  const formatTime = (timeStr: string) => {
    const date = new Date(timeStr);
    return date.toLocaleString('zh-CN');
  };

  // 格式化持续时间
  const formatDuration = (ms?: number) => {
    if (!ms) return '-';
    if (ms < 1000) return `${ms}ms`;
    return `${(ms / 1000).toFixed(1)}s`;
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      {/* 头部 */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900">生成历史</h3>
          <div className="flex items-center gap-2">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`p-2 rounded-lg transition-colors ${
                showFilters ? 'bg-purple-100 text-purple-700' : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
              }`}
              title="过滤器"
            >
              <Filter className="w-4 h-4" />
            </button>
            <button
              onClick={() => loadHistory(true)}
              disabled={loading}
              className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors disabled:opacity-50"
              title="刷新"
            >
              <RotateCcw className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* 过滤器 */}
        {showFilters && (
          <div className="mt-4 p-3 bg-gray-50 rounded-lg space-y-3">
            <div className="flex items-center gap-4">
              <div className="flex-1">
                <label className="block text-xs font-medium text-gray-600 mb-1">状态</label>
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value as GenerationStatus | '')}
                  className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-purple-500 focus:border-transparent"
                >
                  <option value="">全部状态</option>
                  <option value={GenerationStatus.Pending}>等待中</option>
                  <option value={GenerationStatus.Processing}>生成中</option>
                  <option value={GenerationStatus.Completed}>已完成</option>
                  <option value={GenerationStatus.Failed}>失败</option>
                </select>
              </div>
              
              <div className="flex-1">
                <label className="block text-xs font-medium text-gray-600 mb-1">搜索</label>
                <div className="relative">
                  <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 w-3 h-3 text-gray-400" />
                  <input
                    type="text"
                    value={searchText}
                    onChange={(e) => setSearchText(e.target.value)}
                    placeholder="搜索提示词..."
                    className="w-full pl-7 pr-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-purple-500 focus:border-transparent"
                  />
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* 错误提示 */}
      {error && (
        <div className="p-4 bg-red-50 border-b border-red-200 flex items-center gap-2 text-red-700">
          <AlertCircle className="w-4 h-4 flex-shrink-0" />
          <span className="text-sm">{error}</span>
        </div>
      )}

      {/* 记录列表 */}
      <div className="divide-y divide-gray-200">
        {records.length === 0 && !loading ? (
          <div className="p-8 text-center">
            <ImageIcon className="w-12 h-12 text-gray-400 mx-auto mb-2" />
            <p className="text-gray-500">暂无生成记录</p>
          </div>
        ) : (
          records.map((record) => {
            const StatusIcon = STATUS_ICONS[record.status];
            return (
              <div key={record.id} className="p-4 hover:bg-gray-50 transition-colors">
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-2">
                      <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${STATUS_COLORS[record.status]}`}>
                        <StatusIcon className="w-3 h-3" />
                        {STATUS_LABELS[record.status]}
                      </span>
                      <span className="text-xs text-gray-500">
                        {formatTime(record.created_at)}
                      </span>
                      {record.generation_time_ms && (
                        <span className="text-xs text-gray-500">
                          耗时: {formatDuration(record.generation_time_ms)}
                        </span>
                      )}
                    </div>
                    
                    <p className="text-sm text-gray-900 mb-1 line-clamp-2">
                      {record.prompt}
                    </p>
                    
                    {record.error_message && (
                      <p className="text-xs text-red-600 mb-2">
                        错误: {record.error_message}
                      </p>
                    )}
                    
                    <div className="flex items-center gap-4 text-xs text-gray-500">
                      <span>结果: {record.result_image_urls.length} 张图片</span>
                      {record.product_image_path && (
                        <span>商品图片: {record.product_image_path.split('/').pop()}</span>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-1 ml-4">
                    <button
                      onClick={() => handleViewDetail(record)}
                      className="p-1 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded transition-colors"
                      title="查看详情"
                    >
                      <Eye className="w-4 h-4" />
                    </button>
                    
                    {record.status === GenerationStatus.Failed && (
                      <button
                        onClick={() => handleRetry(record)}
                        className="p-1 text-orange-500 hover:text-orange-700 hover:bg-orange-50 rounded transition-colors"
                        title="重试"
                      >
                        <RotateCcw className="w-4 h-4" />
                      </button>
                    )}
                    
                    <button
                      onClick={() => setDeleteConfirm(record.id)}
                      className="p-1 text-red-500 hover:text-red-700 hover:bg-red-50 rounded transition-colors"
                      title="删除"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            );
          })
        )}
      </div>

      {/* 加载更多 */}
      {hasMore && (
        <div className="p-4 border-t border-gray-200 text-center">
          <button
            onClick={loadMore}
            disabled={loading}
            className="px-4 py-2 text-sm text-purple-600 hover:text-purple-700 hover:bg-purple-50 rounded-lg transition-colors disabled:opacity-50"
          >
            {loading ? (
              <>
                <LoadingSpinner size="small" />
                加载中...
              </>
            ) : (
              `加载更多 (${records.length}/${totalCount})`
            )}
          </button>
        </div>
      )}

      {/* 详情模态框 */}
      {showDetailModal && selectedRecord && (
        <Modal
          isOpen={showDetailModal}
          onClose={() => setShowDetailModal(false)}
          title="生成详情"
          size="lg"
        >
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium text-gray-700">状态:</span>
                <span className={`ml-2 inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${STATUS_COLORS[selectedRecord.status]}`}>
                  {React.createElement(STATUS_ICONS[selectedRecord.status], { className: "w-3 h-3" })}
                  {STATUS_LABELS[selectedRecord.status]}
                </span>
              </div>
              <div>
                <span className="font-medium text-gray-700">创建时间:</span>
                <span className="ml-2 text-gray-600">{formatTime(selectedRecord.created_at)}</span>
              </div>
              {selectedRecord.generation_time_ms && (
                <div>
                  <span className="font-medium text-gray-700">生成耗时:</span>
                  <span className="ml-2 text-gray-600">{formatDuration(selectedRecord.generation_time_ms)}</span>
                </div>
              )}
            </div>
            
            <div>
              <span className="font-medium text-gray-700">提示词:</span>
              <p className="mt-1 text-sm text-gray-600 bg-gray-50 p-2 rounded">{selectedRecord.prompt}</p>
            </div>
            
            {selectedRecord.negative_prompt && (
              <div>
                <span className="font-medium text-gray-700">负面提示词:</span>
                <p className="mt-1 text-sm text-gray-600 bg-gray-50 p-2 rounded">{selectedRecord.negative_prompt}</p>
              </div>
            )}
            
            {selectedRecord.error_message && (
              <div>
                <span className="font-medium text-gray-700">错误信息:</span>
                <p className="mt-1 text-sm text-red-600 bg-red-50 p-2 rounded">{selectedRecord.error_message}</p>
              </div>
            )}
            
            {selectedRecord.result_image_urls.length > 0 && (
              <div>
                <span className="font-medium text-gray-700">生成结果:</span>
                <div className="mt-2 grid grid-cols-2 gap-2">
                  {selectedRecord.result_image_urls.map((url, index) => (
                    <div key={index} className="relative group">
                      <img
                        src={url}
                        alt={`结果 ${index + 1}`}
                        className="w-full rounded-lg"
                      />
                      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100">
                        <button
                          onClick={() => {
                            const a = document.createElement('a');
                            a.href = url;
                            a.download = `result_${index + 1}.jpg`;
                            a.click();
                          }}
                          className="p-2 bg-white text-gray-700 rounded-lg hover:bg-gray-100 transition-colors"
                          title="下载"
                        >
                          <Download className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </Modal>
      )}

      {/* 删除确认对话框 */}
      {deleteConfirm && (
        <DeleteConfirmDialog
          isOpen={!!deleteConfirm}
          onCancel={() => setDeleteConfirm(null)}
          onConfirm={() => handleDelete(deleteConfirm)}
          title="删除生成记录"
          message="确定要删除这条生成记录吗？此操作不可撤销。"
        />
      )}
    </div>
  );
};
