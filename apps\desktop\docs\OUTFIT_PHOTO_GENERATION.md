# 穿搭照片生成功能文档

## 概述

穿搭照片生成功能是基于 ComfyUI 工作流的 AI 图像生成系统，允许用户通过选择模特形象和商品图片，结合文本提示词，生成个性化的穿搭照片。

## 功能特性

### 核心功能
- **模特形象选择**: 从项目中的模特照片中选择基础形象
- **商品图片上传**: 支持拖拽上传多种格式的商品图片
- **智能提示词**: 支持正面和负面提示词，精确控制生成效果
- **高级参数配置**: 可调整生成步数、CFG比例、种子值等参数
- **实时进度监控**: 显示生成进度和状态信息
- **历史记录管理**: 查看、重试、删除历史生成记录

### 技术特性
- **ComfyUI 集成**: 基于 ComfyUI 的强大工作流引擎
- **云端存储**: 自动上传生成结果到云端存储
- **批量处理**: 支持批量生成多张穿搭照片
- **错误恢复**: 自动重试机制和错误处理
- **性能监控**: 生成时间统计和性能分析

## 系统架构

### 前端组件结构
```
src/components/outfit/
├── OutfitPhotoGenerator.tsx          # 主生成器组件
├── OutfitPhotoGenerationHistory.tsx  # 历史记录组件
├── ComfyUISettingsPanel.tsx          # 设置面板组件
└── index.ts                          # 组件导出

src/pages/
└── OutfitPhotoGeneration.tsx         # 主页面组件

src/types/
└── outfitPhotoGeneration.ts          # 类型定义

src/services/
└── outfitPhotoGenerationService.ts   # 服务层
```

### 后端架构
```
src-tauri/src/
├── business/services/
│   └── comfyui_service.rs            # ComfyUI 服务
├── presentation/commands/
│   └── outfit_photo_generation_commands.rs  # Tauri 命令
└── data/models/
    └── outfit_photo_generation.rs    # 数据模型
```

## 使用指南

### 基本使用流程

1. **选择模特形象**
   - 从可用的模特照片中选择一张作为基础形象
   - 支持个人形象照片类型

2. **上传商品图片**
   - 拖拽或点击选择商品图片
   - 支持 PNG、JPG、JPEG、GIF、BMP、WebP 格式
   - 可上传多张图片（当前版本使用第一张）

3. **输入提示词**
   - 正面提示词：描述期望的穿搭效果
   - 负面提示词：描述不希望出现的元素（可选）

4. **配置高级参数**（可选）
   - 生成步数：控制生成质量（1-100）
   - CFG 比例：控制提示词遵循程度（1-20）
   - 种子值：控制随机性（-1 为随机）
   - 去噪强度：控制生成强度（0-1）

5. **开始生成**
   - 点击"开始生成"按钮
   - 实时查看生成进度
   - 等待生成完成

### 高级功能

#### 历史记录管理
- 查看所有生成记录
- 按状态、时间筛选
- 重试失败的生成任务
- 删除不需要的记录

#### ComfyUI 设置
- 配置服务器地址和端口
- 设置超时时间
- 测试连接状态
- 管理工作流和输出目录

## API 接口

### 主要 Tauri 命令

```rust
// 创建生成任务
create_outfit_photo_generation_task(request: OutfitPhotoGenerationRequest) -> String

// 执行生成
execute_outfit_photo_generation(generation_id: String) -> OutfitPhotoGenerationResponse

// 获取历史记录
get_outfit_photo_generation_history(query: GenerationHistoryQuery) -> GenerationHistoryResponse

// 重试生成
retry_outfit_photo_generation(generation_id: String) -> OutfitPhotoGenerationResponse

// 删除记录
delete_outfit_photo_generation(generation_id: String) -> ()

// ComfyUI 设置
get_comfyui_settings() -> ComfyUISettings
update_comfyui_settings(settings: ComfyUISettings) -> ()
test_comfyui_connection() -> bool
```

### 数据类型

```typescript
// 生成请求
interface OutfitPhotoGenerationRequest {
  project_id: string;
  model_id: string;
  product_image_path: string;
  prompt: string;
  negative_prompt?: string;
  workflow_config?: WorkflowConfig;
}

// 生成响应
interface OutfitPhotoGenerationResponse {
  id: string;
  status: GenerationStatus;
  result_image_urls: string[];
  error_message?: string;
  generation_time_ms?: number;
}

// 生成状态
enum GenerationStatus {
  Pending = "Pending",
  Processing = "Processing",
  Completed = "Completed",
  Failed = "Failed"
}
```

## 配置说明

### ComfyUI 服务器配置

```typescript
interface ComfyUISettings {
  server_address: string;      // 服务器地址
  server_port: number;         // 端口号
  is_local: boolean;           // 是否本地服务器
  timeout_seconds: number;     // 超时时间
  enabled: boolean;            // 是否启用
  workflow_directory?: string; // 工作流目录
  output_directory?: string;   // 输出目录
}
```

### 默认配置
- 服务器地址: `localhost`
- 端口: `8188`
- 超时时间: `300` 秒
- 生成步数: `20`
- CFG 比例: `7.0`
- 去噪强度: `1.0`

## 故障排除

### 常见问题

1. **ComfyUI 连接失败**
   - 检查 ComfyUI 服务器是否运行
   - 验证服务器地址和端口配置
   - 确认网络连接正常

2. **生成失败**
   - 检查提示词是否合理
   - 验证商品图片格式和大小
   - 查看错误日志信息

3. **生成速度慢**
   - 调整生成步数参数
   - 检查服务器性能
   - 优化工作流配置

### 错误代码

- `CONNECTION_FAILED`: ComfyUI 连接失败
- `INVALID_IMAGE`: 图片格式不支持
- `GENERATION_TIMEOUT`: 生成超时
- `WORKFLOW_ERROR`: 工作流执行错误

## 性能优化

### 前端优化
- 图片懒加载
- 组件按需加载
- 状态管理优化
- 内存泄漏防护

### 后端优化
- 连接池管理
- 异步任务处理
- 错误重试机制
- 资源清理

## 扩展开发

### 添加新的工作流
1. 在 ComfyUI 中创建工作流
2. 导出工作流 JSON 文件
3. 添加到工作流目录
4. 更新工作流模板配置

### 自定义生成参数
1. 扩展 `WorkflowConfig` 类型
2. 更新前端参数面板
3. 修改后端参数处理逻辑
4. 测试参数效果

## 更新日志

### v1.0.0 (2024-01-30)
- 初始版本发布
- 基础生成功能
- ComfyUI 集成
- 历史记录管理
- 设置面板

### 计划功能
- 批量生成优化
- 更多工作流模板
- 生成结果评分
- 社区分享功能
