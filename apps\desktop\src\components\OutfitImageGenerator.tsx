import React, { useState, useCallback } from 'react';
import { open } from '@tauri-apps/plugin-dialog';
import {
  Upload,
  Sparkles,
  X,
  AlertCircle,
  Image as ImageIcon,
  Wand2,
  Settings
} from 'lucide-react';
import { ModelPhoto, PhotoType } from '../types/model';
import { OutfitImageGenerationRequest } from '../types/outfitImage';
import { getImageSrc } from '../utils/imagePathUtils';

interface OutfitImageGeneratorProps {
  modelId: string;
  modelPhotos: ModelPhoto[];
  onGenerate: (request: OutfitImageGenerationRequest) => Promise<void>;
  isGenerating?: boolean;
  disabled?: boolean;
}

const SUPPORTED_IMAGE_FORMATS = ['png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp'];

/**
 * 穿搭图片生成组件
 * 支持选择模特形象图片、上传商品图片、设置生成参数
 */
export const OutfitImageGenerator: React.FC<OutfitImageGeneratorProps> = ({
  modelId,
  modelPhotos,
  onGenerate,
  isGenerating = false,
  disabled = false
}) => {
  const [selectedModelImageId, setSelectedModelImageId] = useState<string>('');
  const [productImages, setProductImages] = useState<string[]>([]);
  const [generationPrompt, setGenerationPrompt] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [dragOver, setDragOver] = useState(false);

  // 获取个人形象照片（用于穿搭生成）
  const portraitPhotos = modelPhotos.filter(photo => photo.photo_type === PhotoType.Portrait);

  // 处理商品图片选择
  const handleProductImageSelect = useCallback(async () => {
    if (disabled || isGenerating) return;

    try {
      setError(null);
      
      const selected = await open({
        multiple: true,
        filters: [
          {
            name: '图像文件',
            extensions: SUPPORTED_IMAGE_FORMATS,
          },
        ],
      });

      if (selected && Array.isArray(selected)) {
        if (selected.length > 10) {
          setError('最多只能选择 10 个商品图片');
          return;
        }
        
        setProductImages(prev => [...prev, ...selected]);
      }
    } catch (error) {
      console.error('Failed to select product images:', error);
      setError('商品图片选择失败');
    }
  }, [disabled, isGenerating]);

  // 处理拖拽上传
  const handleDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (!disabled && !isGenerating) {
      setDragOver(true);
    }
  }, [disabled, isGenerating]);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragOver(false);
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragOver(false);

    if (disabled || isGenerating) return;

    const files = Array.from(e.dataTransfer.files);
    const imagePaths = files
      .filter(file => {
        const extension = file.name.split('.').pop()?.toLowerCase();
        return extension && SUPPORTED_IMAGE_FORMATS.includes(extension);
      })
      .slice(0, 10)
      .map(file => (file as any).path || file.name);

    if (imagePaths.length > 0) {
      setError(null);
      setProductImages(prev => [...prev, ...imagePaths]);
    } else {
      setError('请选择有效的图片文件');
    }
  }, [disabled, isGenerating]);

  // 移除商品图片
  const removeProductImage = useCallback((index: number) => {
    setProductImages(prev => prev.filter((_, i) => i !== index));
  }, []);



  // 处理生成请求
  const handleGenerate = useCallback(async () => {
    if (!selectedModelImageId) {
      setError('请选择模特形象图片');
      return;
    }

    if (productImages.length === 0) {
      setError('请至少上传一张商品图片');
      return;
    }

    try {
      setError(null);
      
      const request: OutfitImageGenerationRequest = {
        model_id: modelId,
        model_image_id: selectedModelImageId,
        product_image_paths: productImages,
        generation_prompt: generationPrompt || undefined,
        style_preferences: undefined
      };

      await onGenerate(request);

      // 生成成功后清空表单
      setSelectedModelImageId('');
      setProductImages([]);
      setGenerationPrompt('');
    } catch (error) {
      console.error('生成穿搭图片失败:', error);
      setError(`生成穿搭图片失败: ${error}`);
    }
  }, [modelId, selectedModelImageId, productImages, generationPrompt, onGenerate]);

  const canGenerate = selectedModelImageId && productImages.length > 0 && !isGenerating && !disabled;

  return (
    <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6 space-y-6">
      <div className="flex items-center mb-6">
        <div className="w-2 h-8 bg-gradient-to-b from-purple-500 to-purple-600 rounded-full mr-4"></div>
        <h2 className="text-xl font-bold text-gray-900">穿搭图片生成</h2>
        <Sparkles className="w-6 h-6 text-purple-500 ml-2" />
      </div>

      {/* 选择模特形象图片 */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900 flex items-center">
          <ImageIcon className="w-5 h-5 mr-2" />
          选择模特形象图片
        </h3>
        
        {portraitPhotos.length === 0 ? (
          <div className="text-center py-8 bg-gray-50 rounded-xl">
            <ImageIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">暂无个人形象照片，请先上传形象照片</p>
          </div>
        ) : (
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
            {portraitPhotos.map((photo) => (
              <div
                key={photo.id}
                className={`relative aspect-square rounded-xl overflow-hidden border-2 cursor-pointer transition-all duration-200 ${
                  selectedModelImageId === photo.id
                    ? 'border-purple-500 ring-2 ring-purple-200 scale-105'
                    : 'border-gray-200 hover:border-purple-300 hover:scale-102'
                }`}
                onClick={() => setSelectedModelImageId(photo.id)}
              >
                <img
                  src={getImageSrc(photo.file_path)}
                  alt={photo.file_name}
                  className="w-full h-full object-cover"
                />
                {selectedModelImageId === photo.id && (
                  <div className="absolute inset-0 bg-purple-500 bg-opacity-20 flex items-center justify-center">
                    <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                      <Sparkles className="w-5 h-5 text-white" />
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* 上传商品图片 */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900 flex items-center">
          <Upload className="w-5 h-5 mr-2" />
          上传商品图片
          <span className="text-sm text-gray-500 ml-2">({productImages.length}/10)</span>
        </h3>

        {/* 拖拽上传区域 */}
        <div
          className={`border-2 border-dashed rounded-xl p-6 text-center transition-all duration-200 ${
            dragOver
              ? 'border-purple-400 bg-purple-50'
              : disabled || isGenerating
              ? 'border-gray-200 bg-gray-50'
              : 'border-gray-300 bg-white hover:border-purple-400 hover:bg-purple-50'
          } ${disabled || isGenerating ? 'cursor-not-allowed' : 'cursor-pointer'}`}
          onDragEnter={handleDragEnter}
          onDragLeave={handleDragLeave}
          onDragOver={handleDragOver}
          onDrop={handleDrop}
          onClick={handleProductImageSelect}
        >
          <div className="flex flex-col items-center">
            <div className={`p-3 rounded-full mb-4 ${
              dragOver ? 'bg-purple-100' : 'bg-gray-100'
            }`}>
              <Upload className={`w-8 h-8 ${
                dragOver ? 'text-purple-600' : 'text-gray-400'
              }`} />
            </div>
            
            <h4 className="text-lg font-medium text-gray-900 mb-2">
              {dragOver ? '释放以上传商品图片' : '上传商品图片'}
            </h4>
            
            <p className="text-sm text-gray-500 mb-4">
              拖拽图片到此处，或点击选择文件
            </p>
            
            <div className="flex items-center space-x-4 text-xs text-gray-400">
              <span>支持格式：{SUPPORTED_IMAGE_FORMATS.join(', ').toUpperCase()}</span>
              <span>•</span>
              <span>最多 10 个文件</span>
            </div>
          </div>
        </div>

        {/* 已上传的商品图片 */}
        {productImages.length > 0 && (
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-4">
            {productImages.map((imagePath, index) => (
              <div key={index} className="relative group">
                <div className="aspect-square rounded-lg overflow-hidden border border-gray-200">
                  <img
                    src={getImageSrc(imagePath)}
                    alt={`商品图片 ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                </div>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    removeProductImage(index);
                  }}
                  className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center hover:bg-red-600"
                  title="删除图片"
                >
                  <X className="w-3 h-3" />
                </button>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* 生成参数设置 */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900 flex items-center">
          <Settings className="w-5 h-5 mr-2" />
          生成参数设置
        </h3>

        {/* 生成提示词 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            生成提示词（可选）
          </label>
          <textarea
            value={generationPrompt}
            onChange={(e) => setGenerationPrompt(e.target.value)}
            placeholder="描述您希望生成的穿搭风格，例如：时尚、休闲、商务、运动等..."
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none"
            rows={3}
            disabled={disabled || isGenerating}
          />
        </div>


      </div>

      {/* 错误提示 */}
      {error && (
        <div className="flex items-center p-3 bg-red-50 border border-red-200 rounded-lg">
          <AlertCircle className="w-5 h-5 text-red-500 mr-2 flex-shrink-0" />
          <span className="text-sm text-red-700">{error}</span>
          <button
            onClick={() => setError(null)}
            className="ml-auto text-red-500 hover:text-red-700"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      )}

      {/* 生成按钮 */}
      <div className="flex justify-end pt-4 border-t border-gray-200">
        <button
          onClick={handleGenerate}
          disabled={!canGenerate}
          className={`flex items-center px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
            canGenerate
              ? 'bg-purple-600 text-white hover:bg-purple-700 hover:scale-105 shadow-lg hover:shadow-xl'
              : 'bg-gray-300 text-gray-500 cursor-not-allowed'
          }`}
        >
          {isGenerating ? (
            <>
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
              生成中...
            </>
          ) : (
            <>
              <Wand2 className="w-5 h-5 mr-2" />
              生成穿搭图片
            </>
          )}
        </button>
      </div>

      {/* 使用说明 */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start">
          <Sparkles className="w-5 h-5 text-blue-500 mr-2 flex-shrink-0 mt-0.5" />
          <div className="text-sm text-blue-700">
            <p className="font-medium mb-1">使用说明：</p>
            <ul className="space-y-1 text-xs">
              <li>• 选择一张模特的个人形象照片作为基础</li>
              <li>• 上传要搭配的商品图片（服装、配饰等）</li>
              <li>• 可选择添加生成提示词来指导AI生成</li>
              <li>• 点击生成按钮，AI将为您创建穿搭效果图</li>
              <li>• 生成的图片数量与上传的商品图片数量相等</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OutfitImageGenerator;
