import React, { useEffect } from 'react';
import { createPortal } from 'react-dom';
import { X, Sparkles } from 'lucide-react';
import { Model } from '../types/model';
import { OutfitImageGenerationRequest } from '../types/outfitImage';
import { OutfitImageGenerator } from './OutfitImageGenerator';

interface OutfitImageGenerationModalProps {
  isOpen: boolean;
  onClose: () => void;
  model: Model;
  onGenerate: (request: OutfitImageGenerationRequest) => Promise<void>;
  isGenerating?: boolean;
}

/**
 * 穿搭图片生成模态框组件
 * 使用Portal渲染到modal-root容器，避免复杂容器结构影响
 */
export const OutfitImageGenerationModal: React.FC<OutfitImageGenerationModalProps> = ({
  isOpen,
  onClose,
  model,
  onGenerate,
  isGenerating = false
}) => {
  // 键盘事件处理
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      // 防止背景滚动
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  // 获取modal-root容器
  const modalRoot = document.getElementById('modal-root');
  if (!modalRoot) return null;

  // 使用Portal渲染到modal-root容器
  return createPortal(
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      {/* 模态框背景 */}
      <div 
        className="absolute inset-0 cursor-pointer"
        onClick={onClose}
      />
      
      {/* 模态框内容 */}
      <div className="relative bg-white rounded-2xl shadow-2xl max-w-7xl max-h-[95vh] w-full mx-4 flex flex-col overflow-hidden">
        {/* 头部 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-purple-50 to-pink-50">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
              <Sparkles className="w-6 h-6 text-white" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-gray-900">穿搭图片生成</h2>
              <p className="text-sm text-gray-600">为 {model.name} 生成AI穿搭效果图</p>
            </div>
          </div>
          
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
            title="关闭 (Esc)"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* 主要内容区域 */}
        <div className="flex-1 overflow-y-auto p-6">
          <div className="max-w-2xl mx-auto">
            {/* 穿搭图片生成器 */}
            <OutfitImageGenerator
              modelId={model.id}
              modelPhotos={model.photos}
              onGenerate={onGenerate}
              isGenerating={isGenerating}
            />
          </div>
        </div>


      </div>
    </div>,
    modalRoot
  );
};

export default OutfitImageGenerationModal;
