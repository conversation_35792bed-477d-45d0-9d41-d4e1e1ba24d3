/**
 * 穿搭照片生成相关类型定义
 * 基于 ComfyUI 工作流的穿搭照片生成功能
 */

// 生成状态枚举
export enum GenerationStatus {
  Pending = "Pending",
  Processing = "Processing", 
  Completed = "Completed",
  Failed = "Failed"
}

// 工作流进度信息
export interface WorkflowProgress {
  stage: string;
  progress: number;
  message?: string;
  current_step?: number;
  total_steps?: number;
}

// 工作流配置
export interface WorkflowConfig {
  workflow_file_path: string;
  steps?: number;
  cfg_scale?: number;
  seed?: number;
  sampler?: string;
  scheduler?: string;
  denoise?: number;
}

// 穿搭照片生成请求
export interface OutfitPhotoGenerationRequest {
  project_id: string;
  model_id: string;
  product_image_path: string;
  prompt: string;
  negative_prompt?: string;
  workflow_config?: WorkflowConfig;
}

// 穿搭照片生成响应
export interface OutfitPhotoGenerationResponse {
  id: string;
  status: GenerationStatus;
  result_image_urls: string[];
  error_message?: string;
  generation_time_ms?: number;
}

// 穿搭照片生成记录（完整数据）
export interface OutfitPhotoGeneration {
  id: string;
  project_id: string;
  model_id: string;
  product_image_path: string;
  product_image_url?: string;
  prompt: string;
  negative_prompt?: string;
  status: GenerationStatus;
  workflow_id?: string;
  comfyui_prompt_id?: string;
  result_image_paths: string[];
  result_image_urls: string[];
  error_message?: string;
  started_at: string;
  completed_at?: string;
  generation_time_ms?: number;
  created_at: string;
  updated_at: string;
}

// 批量生成进度信息
export interface BatchGenerationProgress {
  current_index: number;
  total_count: number;
  current_progress: WorkflowProgress;
  completed_count: number;
  failed_count: number;
}

// ComfyUI 设置
export interface ComfyUISettings {
  server_address: string;
  server_port: number;
  is_local: boolean;
  timeout_seconds: number;
  enabled: boolean;
  workflow_directory?: string;
  output_directory?: string;
}

// 云端上传统计
export interface CloudUploadStatistics {
  total_uploads: number;
  successful_uploads: number;
  failed_uploads: number;
  pending_uploads: number;
  total_size_bytes: number;
  average_upload_time_ms: number;
  last_upload_time?: string;
}

// 生成历史记录查询参数
export interface GenerationHistoryQuery {
  project_id?: string;
  model_id?: string;
  status?: GenerationStatus;
  start_date?: string;
  end_date?: string;
  page?: number;
  page_size?: number;
}

// 生成历史记录响应
export interface GenerationHistoryResponse {
  records: OutfitPhotoGeneration[];
  total_count: number;
  page: number;
  page_size: number;
  has_more: boolean;
}

// 工作流模板信息
export interface WorkflowTemplate {
  id: string;
  name: string;
  description?: string;
  file_path: string;
  preview_image?: string;
  default_config: WorkflowConfig;
  supported_features: string[];
  created_at: string;
  updated_at: string;
}

// 生成预设
export interface GenerationPreset {
  id: string;
  name: string;
  description?: string;
  workflow_config: WorkflowConfig;
  default_prompt: string;
  default_negative_prompt?: string;
  tags: string[];
  is_favorite: boolean;
  created_at: string;
  updated_at: string;
}

// 模特图片选择项
export interface ModelImageOption {
  id: string;
  model_id: string;
  image_path: string;
  image_url?: string;
  thumbnail_url?: string;
  description?: string;
  photo_type: string;
  is_primary: boolean;
}

// 商品图片信息
export interface ProductImageInfo {
  id: string;
  file_path: string;
  file_name: string;
  file_size: number;
  mime_type: string;
  upload_url?: string;
  thumbnail_url?: string;
  description?: string;
  created_at: string;
}

// 生成任务队列项
export interface GenerationQueueItem {
  id: string;
  request: OutfitPhotoGenerationRequest;
  priority: number;
  created_at: string;
  estimated_duration_ms?: number;
  dependencies?: string[];
}

// 生成任务队列状态
export interface GenerationQueueStatus {
  total_items: number;
  pending_items: number;
  processing_items: number;
  completed_items: number;
  failed_items: number;
  estimated_wait_time_ms?: number;
}
